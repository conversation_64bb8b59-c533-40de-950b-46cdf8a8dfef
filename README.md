# Namecheap Proxy API

A Node.js Express TypeScript application that serves as a proxy for the Namecheap API, containerized with Docker and served through Nginx reverse proxy.

## Features

- 🚀 Express.js with TypeScript
- 🔒 Security middleware (Helmet, CORS)
- 📝 Request logging with Morgan
- 🐳 Docker containerization
- 🌐 Nginx reverse proxy
- 🏥 Health checks
- 📊 Rate limiting
- 🛡️ Error handling and validation

## API Endpoints

### Health Check
```
GET /health
```
Returns the health status of the API.

### Proxy Endpoint
The proxy accepts both GET and POST requests, forwards them to `https://api.sandbox.namecheap.com/xml.response`, and converts the XML response to JSON format.

**GET Request (query parameters):**
```
GET /api/proxy?Command=namecheap.domains.check&DomainList=example.com,test.org
```

**POST Request (JSON body):**
```
POST /api/proxy
Content-Type: application/json

{
  "Command": "namecheap.domains.check",
  "DomainList": "example.com,test.org"
}
```

**Response Format:**
```json
{
  "success": true,
  "data": {
    // Parsed JSON from XML response
  },
  "originalXml": "<?xml version=\"1.0\" encoding=\"utf-8\"?>..."
}
```

The proxy automatically adds these parameters from environment variables:
- ApiUser (default: blackingdev)
- ApiKey (default: 84523b0158f340c9be472d6a8d50c309)
- UserName (default: blackingdev)
- ClientIp (default: *************)

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Node.js 18+ (for local development)

### Using Docker (Recommended)

1. Clone the repository:
```bash
git clone <repository-url>
cd namecheap-proxy
```

2. Copy environment file:
```bash
cp .env.example .env
```

3. Build and run with Docker Compose:
```bash
# Production
docker-compose up --build

# Development (with hot reload)
docker-compose -f docker-compose.dev.yml up --build
```

4. Access the application:
- Production: http://localhost
- Development: http://localhost:8080

### Local Development

1. Install dependencies:
```bash
npm install
```

2. Copy environment file:
```bash
cp .env.example .env
```

3. Run in development mode:
```bash
npm run dev
```

4. Build for production:
```bash
npm run build
npm start
```

## Configuration

The application uses environment variables for configuration. See `.env.example` for all available options.

### Environment Variables

- `NODE_ENV`: Application environment (development/production)
- `PORT`: Server port (default: 3000)
- `NAMECHEAP_API_USER`: Namecheap API username (default: blackingdev)
- `NAMECHEAP_API_KEY`: Namecheap API key (default: 84523b0158f340c9be472d6a8d50c309)
- `NAMECHEAP_USERNAME`: Namecheap username (default: blackingdev)
- `NAMECHEAP_CLIENT_IP`: Client IP address (default: *************)
- `NAMECHEAP_BASE_URL`: Namecheap API base URL (default: https://api.sandbox.namecheap.com/xml.response)

## Project Structure

```
├── src/
│   ├── config/          # Configuration files
│   ├── controllers/     # Route controllers
│   ├── middleware/      # Express middleware
│   ├── types/          # TypeScript type definitions
│   ├── utils/          # Utility functions
│   └── index.ts        # Application entry point
├── nginx/              # Nginx configuration
├── docker-compose.yml  # Production Docker Compose
├── docker-compose.dev.yml # Development Docker Compose
├── Dockerfile          # Docker image definition
└── package.json        # Node.js dependencies
```

## Docker Services

- **api**: Node.js Express application
- **nginx**: Nginx reverse proxy

## Health Checks

Both services include health checks:
- API: HTTP GET to `/health`
- Nginx: Configuration validation

## Security Features

- Helmet.js for security headers
- CORS configuration
- Rate limiting (10 requests/second)
- Input validation
- Error sanitization in production

## Logging

- Request logging with Morgan
- Custom application logging
- Structured log format with timestamps

## License

MIT
# namecheap-proxy
